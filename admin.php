<?php
// admin.php
require_once 'config.php';
require_once 'db_connect.php';

// Vérifier si l'utilisateur est connecté en tant qu'admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header("Location: admin_login.php");
    exit;
}

// Générer un jeton CSRF si absent
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Obtenir la connexion à la base de données
try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    $error_message = "Impossible de se connecter à la base de données : " . htmlspecialchars($e->getMessage());
    file_put_contents('debug.log', "Admin.php: {$e->getMessage()} à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    exit($error_message);
}

// Journaliser la version de PHP pour le débogage
file_put_contents('debug.log', "Admin.php: Version PHP " . PHP_VERSION . " à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);

// Initialiser les messages
$product_message = '';
$category_message = '';

// Fonction pour gérer le téléchargement d'image
function handleImageUpload($file, $existing_image = null) {
    if ($file['error'] == UPLOAD_ERR_NO_FILE) {
        return $existing_image ?: 'Uploads/placeholder.jpg';
    }

    if ($file['error'] != UPLOAD_ERR_OK) {
        throw new Exception("Erreur lors du téléchargement de l'image : Code d'erreur " . $file['error']);
    }

    $target_dir = "Uploads/";
    if (!is_dir($target_dir)) {
        mkdir($target_dir, 0755, true);
        file_put_contents('debug.log', "Admin.php: Création du répertoire Uploads à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    }

    // Préserver les caractères UTF-8 dans les noms de fichiers tout en gardant la sécurité
    $original_name = basename($file["name"]);
    $file_extension = pathinfo($original_name, PATHINFO_EXTENSION);
    $file_name_without_ext = pathinfo($original_name, PATHINFO_FILENAME);

    // Nettoyer le nom de fichier tout en préservant les caractères UTF-8
    $clean_name = preg_replace('/[<>:"/\\|?*]/', '_', $file_name_without_ext);
    $clean_name = mb_substr($clean_name, 0, 50, 'UTF-8'); // Limiter la longueur

    $image_name = time() . '_' . $clean_name . '.' . $file_extension;
    $target_file = $target_dir . $image_name;

    // Vérifier si c'est une image valide
    $check = getimagesize($file["tmp_name"]);
    if ($check === false) {
        file_put_contents('debug.log', "Admin.php: Fichier non reconnu comme image : {$file['name']} (MIME: {$file['type']}) à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
        throw new Exception("Le fichier n'est pas une image valide.");
    }

    // Vérifier la taille (max 5MB, optionnel)
    if ($file['size'] > 5000000) {
        throw new Exception("L'image est trop volumineuse (max 5MB).");
    }

    if (!move_uploaded_file($file["tmp_name"], $target_file)) {
        throw new Exception("Échec du déplacement de l'image vers {$target_file}.");
    }

    // Supprimer l'ancienne image si elle existe et n'est pas le placeholder
    if ($existing_image && $existing_image !== 'Uploads/placeholder.jpg' && file_exists($existing_image)) {
        unlink($existing_image);
        file_put_contents('debug.log', "Admin.php: Ancienne image supprimée {$existing_image} à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    }

    file_put_contents('debug.log', "Admin.php: Image téléchargée à {$target_file} à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    return $target_file;
}

// Gérer l'ajout de produit
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['add_product']) && $_POST['csrf_token'] === $_SESSION['csrf_token']) {
    try {
        // Utiliser FILTER_UNSAFE_RAW pour préserver tous les caractères spéciaux et UTF-8
        $name = trim(filter_input(INPUT_POST, 'product_name', FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_LOW));
        $category_id = filter_input(INPUT_POST, 'category_id', FILTER_VALIDATE_INT);
        // Pour les descriptions, ne pas utiliser FILTER_FLAG_STRIP_LOW pour conserver les espaces et sauts de ligne
        $description_en = filter_input(INPUT_POST, 'description_en', FILTER_UNSAFE_RAW);
        $description_ar = filter_input(INPUT_POST, 'description_ar', FILTER_UNSAFE_RAW);

        // Validation plus flexible pour accepter tous les caractères UTF-8
        // Trim seulement le nom, pas les descriptions pour conserver la mise en forme
        if (empty($name) || $category_id === false || empty(trim($description_en)) || empty(trim($description_ar))) {
            throw new Exception("Veuillez remplir tous les champs requis.");
        }

        // Validation supplémentaire pour s'assurer que les données sont en UTF-8 valide
        if (!mb_check_encoding($name, 'UTF-8') || !mb_check_encoding($description_en, 'UTF-8') || !mb_check_encoding($description_ar, 'UTF-8')) {
            throw new Exception("Caractères non valides détectés. Veuillez utiliser uniquement des caractères UTF-8 valides.");
        }

        // Vérifier si la catégorie existe
        $stmt = $conn->prepare("SELECT id FROM categories WHERE id = :id");
        $stmt->execute([':id' => $category_id]);
        if ($stmt->rowCount() === 0) {
            throw new Exception("Catégorie invalide.");
        }

        $image_path = handleImageUpload($_FILES['product_image']);

        $sql = "INSERT INTO products (name, category_id, description_en, description_ar, image_path)
                VALUES (:name, :category_id, :description_en, :description_ar, :image_path)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            ':name' => $name,
            ':category_id' => $category_id,
            ':description_en' => $description_en,
            ':description_ar' => $description_ar,
            ':image_path' => $image_path
        ]);

        $product_message = "Produit ajouté avec succès !";
        file_put_contents('debug.log', "Admin.php: Produit '{$name}' ajouté avec succès à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    } catch (Exception $e) {
        $product_message = "Erreur lors de l'ajout du produit : " . htmlspecialchars($e->getMessage());
        file_put_contents('debug.log', "Admin.php: Échec de l'ajout du produit : {$e->getMessage()} à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    }
}

// Gérer la mise à jour du produit
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_product']) && $_POST['csrf_token'] === $_SESSION['csrf_token']) {
    try {
        $product_id = filter_input(INPUT_POST, 'product_id', FILTER_VALIDATE_INT);
        // Utiliser FILTER_UNSAFE_RAW pour préserver tous les caractères spéciaux et UTF-8
        $name = trim(filter_input(INPUT_POST, 'product_name', FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_LOW));
        $category_id = filter_input(INPUT_POST, 'category_id', FILTER_VALIDATE_INT);
        // Pour les descriptions, ne pas utiliser FILTER_FLAG_STRIP_LOW pour conserver les espaces et sauts de ligne
        $description_en = filter_input(INPUT_POST, 'description_en', FILTER_UNSAFE_RAW);
        $description_ar = filter_input(INPUT_POST, 'description_ar', FILTER_UNSAFE_RAW);
        $existing_image_path = filter_input(INPUT_POST, 'existing_image_path', FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_LOW);

        // Trim seulement le nom, pas les descriptions pour conserver la mise en forme
        if ($product_id === false || empty($name) || $category_id === false || empty(trim($description_en)) || empty(trim($description_ar))) {
            throw new Exception("Veuillez remplir tous les champs requis.");
        }

        // Validation UTF-8 pour la mise à jour
        if (!mb_check_encoding($name, 'UTF-8') || !mb_check_encoding($description_en, 'UTF-8') || !mb_check_encoding($description_ar, 'UTF-8')) {
            throw new Exception("Caractères non valides détectés. Veuillez utiliser uniquement des caractères UTF-8 valides.");
        }

        // Vérifier si le produit existe
        $stmt = $conn->prepare("SELECT image_path FROM products WHERE id = :id");
        $stmt->execute([':id' => $product_id]);
        if ($stmt->rowCount() === 0) {
            throw new Exception("Produit inexistant.");
        }
        $current_product = $stmt->fetch(PDO::FETCH_ASSOC);

        // Vérifier si la catégorie existe
        $stmt = $conn->prepare("SELECT id FROM categories WHERE id = :id");
        $stmt->execute([':id' => $category_id]);
        if ($stmt->rowCount() === 0) {
            throw new Exception("Catégorie invalide.");
        }

        $image_path = handleImageUpload($_FILES['product_image'], $existing_image_path);

        $sql = "UPDATE products SET name = :name, category_id = :category_id, description_en = :description_en,
                description_ar = :description_ar, image_path = :image_path WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            ':name' => $name,
            ':category_id' => $category_id,
            ':description_en' => $description_en,
            ':description_ar' => $description_ar,
            ':image_path' => $image_path,
            ':id' => $product_id
        ]);

        $product_message = "Produit mis à jour avec succès !";
        file_put_contents('debug.log', "Admin.php: Produit ID {$product_id} mis à jour à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    } catch (Exception $e) {
        $product_message = "Erreur lors de la mise à jour du produit : " . htmlspecialchars($e->getMessage());
        file_put_contents('debug.log', "Admin.php: Échec de la mise à jour du produit : {$e->getMessage()} à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    }
}

// Gérer la suppression du produit
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['delete_product']) && $_POST['csrf_token'] === $_SESSION['csrf_token']) {
    try {
        $product_id = filter_input(INPUT_POST, 'product_id', FILTER_VALIDATE_INT);
        if ($product_id === false) {
            throw new Exception("ID de produit invalide.");
        }

        // Vérifier si le produit existe
        $stmt = $conn->prepare("SELECT image_path FROM products WHERE id = :id");
        $stmt->execute([':id' => $product_id]);
        if ($stmt->rowCount() === 0) {
            throw new Exception("Produit inexistant.");
        }
        $product = $stmt->fetch(PDO::FETCH_ASSOC);

        // Supprimer l'image si elle existe et n'est pas le placeholder
        if ($product['image_path'] !== 'Uploads/placeholder.jpg' && file_exists($product['image_path'])) {
            unlink($product['image_path']);
            file_put_contents('debug.log', "Admin.php: Image supprimée {$product['image_path']} à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
        }

        $sql = "DELETE FROM products WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->execute([':id' => $product_id]);

        $product_message = "Produit supprimé avec succès !";
        file_put_contents('debug.log', "Admin.php: Produit ID {$product_id} supprimé à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    } catch (Exception $e) {
        $product_message = "Erreur lors de la suppression du produit : " . htmlspecialchars($e->getMessage());
        file_put_contents('debug.log', "Admin.php: Échec de la suppression du produit : {$e->getMessage()} à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    }
}

// Gérer l'ajout de catégorie
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['add_category']) && $_POST['csrf_token'] === $_SESSION['csrf_token']) {
    // Utiliser FILTER_UNSAFE_RAW pour préserver tous les caractères UTF-8
    $name_en = trim(filter_input(INPUT_POST, 'category_name_en', FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_LOW));
    $name_ar = trim(filter_input(INPUT_POST, 'category_name_ar', FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_LOW));

    if (!empty($name_en) && !empty($name_ar)) {
        // Validation UTF-8 pour les catégories
        if (!mb_check_encoding($name_en, 'UTF-8') || !mb_check_encoding($name_ar, 'UTF-8')) {
            $category_message = "Caractères non valides détectés. Veuillez utiliser uniquement des caractères UTF-8 valides.";
        } else {
            try {
                $sql = "INSERT INTO categories (name_en, name_ar) VALUES (:name_en, :name_ar)";
                $stmt = $conn->prepare($sql);
                $stmt->execute([
                    ':name_en' => $name_en,
                    ':name_ar' => $name_ar
                ]);
                $category_message = "Category added successfully! / تم إضافة الفئة بنجاح!";
            } catch (PDOException $e) {
                $category_message = "Error adding category / خطأ في إضافة الفئة: " . $e->getMessage();
                file_put_contents('debug.log', "Admin.php: Échec de l'insertion de la catégorie : {$e->getMessage()} à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
            }
        }
    } else {
        $category_message = "Please fill in all required fields / يرجى ملء جميع الحقول المطلوبة";
    }
}

// Gérer la mise à jour de la catégorie (code existant, non modifié)
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_category']) && $_POST['csrf_token'] === $_SESSION['csrf_token']) {
    file_put_contents('debug.log', "Admin.php: Reçu POST update_category : " . json_encode($_POST) . " à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);

    $category_id = filter_input(INPUT_POST, 'category_id', FILTER_VALIDATE_INT);
    // Utiliser FILTER_UNSAFE_RAW pour préserver tous les caractères UTF-8
    $name_en = trim(filter_input(INPUT_POST, 'category_name_en', FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_LOW));
    $name_ar = trim(filter_input(INPUT_POST, 'category_name_ar', FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_LOW));

    file_put_contents('debug.log', "Admin.php: Valeurs filtrées - ID: {$category_id}, name_en: '$name_en', name_ar: '$name_ar' à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);

    if ($category_id === false || $category_id <= 0) {
        $category_message = "ID de catégorie invalide.";
        file_put_contents('debug.log', "Admin.php: ID de catégorie invalide pour la mise à jour : {$category_id} à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    } elseif ($name_en === '' || $name_ar === '') {
        $category_message = "Veuillez remplir tous les champs requis.";
        file_put_contents('debug.log', "Admin.php: Champs requis manquants pour la mise à jour de la catégorie (name_en: '$name_en', name_ar: '$name_ar') à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    } elseif (!mb_check_encoding($name_en, 'UTF-8') || !mb_check_encoding($name_ar, 'UTF-8')) {
        $category_message = "Caractères non valides détectés. Veuillez utiliser uniquement des caractères UTF-8 valides.";
        file_put_contents('debug.log', "Admin.php: Caractères UTF-8 invalides détectés lors de la mise à jour de la catégorie à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
    } else {
        try {
            $stmt_check = $conn->prepare("SELECT id FROM categories WHERE id = :id");
            $stmt_check->execute([':id' => $category_id]);
            if ($stmt_check->rowCount() === 0) {
                $category_message = "La catégorie avec l'ID {$category_id} n'existe pas.";
                file_put_contents('debug.log', "Admin.php: Tentative de mise à jour d'une catégorie inexistante ID: {$category_id} à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
            } else {
                $sql = "UPDATE categories SET name_en = :name_en, name_ar = :name_ar WHERE id = :id";
                $stmt = $conn->prepare($sql);
                $stmt->execute([
                    ':name_en' => $name_en,
                    ':name_ar' => $name_ar,
                    ':id' => $category_id
                ]);
                $category_message = "Catégorie mise à jour avec succès !";
                file_put_contents('debug.log', "Admin.php: Catégorie ID {$category_id} mise à jour en '$name_en'/'$name_ar' à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);

                $categories = $conn->query("SELECT id, name_en, name_ar FROM categories ORDER BY name_en")->fetchAll(PDO::FETCH_ASSOC);
            }
        } catch (PDOException $e) {
            $category_message = "Erreur lors de la mise à jour de la catégorie : " . $e->getMessage();
            file_put_contents('debug.log', "Admin.php: Échec de la mise à jour de la catégorie : {$e->getMessage()} à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
        }
    }
}

// Gérer la suppression de catégorie (code existant, non modifié)
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['delete_category']) && $_POST['csrf_token'] === $_SESSION['csrf_token']) {
    $category_id = filter_input(INPUT_POST, 'category_id', FILTER_VALIDATE_INT);
    if ($category_id) {
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM products WHERE category_id = :id");
            $stmt->execute([':id' => $category_id]);
            if ($stmt->fetchColumn() > 0) {
                $category_message = "Impossible de supprimer la catégorie : Elle est associée à des produits.";
            } else {
                $sql = "DELETE FROM categories WHERE id = :id";
                $stmt = $conn->prepare($sql);
                $stmt->execute([':id' => $category_id]);
                $category_message = "Catégorie supprimée avec succès !";
            }
        } catch (PDOException $e) {
            $category_message = "Erreur lors de la suppression de la catégorie : " . $e->getMessage();
            file_put_contents('debug.log', "Admin.php: Échec de la suppression de la catégorie : {$e->getMessage()} à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
        }
    } else {
        $category_message = "ID de catégorie invalide.";
    }
}

// Récupérer les catégories et les produits
$categories = [];
$products = [];
try {
    $categories = $conn->query("SELECT id, name_en, name_ar FROM categories ORDER BY name_en")->fetchAll(PDO::FETCH_ASSOC);
    $products = $conn->query("SELECT p.id, p.name, p.category_id, p.description_en, p.description_ar, p.image_path, c.name_en AS category_name
                             FROM products p LEFT JOIN categories c ON p.category_id = c.id
                             ORDER BY p.name")->fetchAll(PDO::FETCH_ASSOC);
    file_put_contents('debug.log', "Admin.php: Récupéré " . count($categories) . " catégories et " . count($products) . " produits à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
} catch (PDOException $e) {
    $error_message = "Échec de la récupération des données : " . htmlspecialchars($e->getMessage());
    file_put_contents('debug.log', "Admin.php: Échec de la récupération des données : {$e->getMessage()} à " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panneau d'administration - Madjour Industries</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .admin-section {
            background: #fff;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .admin-section h2 {
            color: #D32F2F;
            margin-bottom: 1.5rem;
        }
        .admin-form {
            display: grid;
            gap: 1rem;
        }
        .admin-form label {
            color: #333;
            font-weight: 500;
        }
        .admin-form input, .admin-form select, .admin-form textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #E0E0E0;
            border-radius: 6px;
            font-size: 1rem;
        }
        .admin-form textarea {
            min-height: 100px;
            resize: vertical;
            white-space: pre-wrap; /* Préserve les espaces et sauts de ligne */
        }
        .admin-form button {
            background: #D32F2F;
            color: #fff;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .admin-form button:hover {
            background: #B71C1C;
        }
        .message {
            color: #10b981;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        .error {
            color: #ef4444;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        .data-table th, .data-table td {
            border: 1px solid #E0E0E0;
            padding: 0.75rem;
            text-align: left;
            color: #000;
        }
        .data-table th {
            background: #D32F2F;
            color: #fff;
        }
        .data-table img {
            max-width: 100px;
            height: auto;
        }
        .action-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: #D32F2F;
            margin-right: 0.5rem;
        }
        .action-btn:hover {
            color: #B71C1C;
        }
        .edit-form {
            display: none;
            margin-top: 1rem;
            padding: 1rem;
            background: #f9f9f9;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php if (isset($error_message)): ?>
            <p class="error"><?php echo htmlspecialchars($error_message); ?></p>
        <?php endif; ?>

        <div class="admin-section">
            <h2>Ajouter un nouveau produit</h2>
            <?php if (!empty($product_message)): ?>
                <p class="<?php echo strpos($product_message, 'Erreur') === false ? 'message' : 'error'; ?>">
                    <?php echo htmlspecialchars($product_message); ?>
                </p>
            <?php endif; ?>
            <form class="admin-form" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>">
                <label for="product_name">Nom du produit</label>
                <input type="text" id="product_name" name="product_name" required maxlength="255">

                <label for="category_id">Catégorie</label>
                <select id="category_id" name="category_id" required>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?php echo htmlspecialchars($category['id']); ?>">
                            <?php echo htmlspecialchars($category['name_en']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>

                <label for="description_en">Description (Anglais)</label>
                <textarea id="description_en" name="description_en" required></textarea>

                <label for="description_ar">Description (Arabe)</label>
                <textarea id="description_ar" name="description_ar" required></textarea>

                <label for="product_image">Image du produit (Facultatif, max 5MB, tous formats d'image)</label>
                <input type="file" id="product_image" name="product_image" accept="image/*">

                <button type="submit" name="add_product">Ajouter le produit</button>
            </form>
        </div>

        <div class="admin-section">
            <h2>Gérer les produits</h2>
            <?php if (empty($products)): ?>
                <p class="error">Aucun produit trouvé.</p>
            <?php else: ?>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nom</th>
                            <th>Catégorie</th>
                            <th>Image</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($product['id']); ?></td>
                                <td><?php echo htmlspecialchars($product['name']); ?></td>
                                <td><?php echo htmlspecialchars($product['category_name'] ?? 'Inconnu'); ?></td>
                                <td><img src="<?php echo htmlspecialchars($product['image_path']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>"></td>
                                <td>
                                    <button class="action-btn edit-product-btn"
                                            data-id="<?php echo htmlspecialchars($product['id']); ?>"
                                            data-name="<?php echo htmlspecialchars($product['name']); ?>"
                                            data-category-id="<?php echo htmlspecialchars($product['category_id']); ?>"
                                            data-desc-en="<?php echo htmlspecialchars($product['description_en']); ?>"
                                            data-desc-ar="<?php echo htmlspecialchars($product['description_ar']); ?>"
                                            data-image-path="<?php echo htmlspecialchars($product['image_path']); ?>">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <form method="POST" style="display:inline;" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce produit ?');">
                                        <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>">
                                        <input type="hidden" name="product_id" value="<?php echo htmlspecialchars($product['id']); ?>">
                                        <button type="submit" name="delete_product" class="action-btn">
                                            <span class="material-icons">delete</span>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>

            <div class="edit-form" id="edit-product-form">
                <h2>Modifier le produit</h2>
                <form class="admin-form" method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>">
                    <input type="hidden" name="product_id" id="edit_product_id">
                    <input type="hidden" name="existing_image_path" id="edit_existing_image_path">
                    <label for="edit_product_name">Nom du produit</label>
                    <input type="text" id="edit_product_name" name="product_name" required maxlength="255">

                    <label for="edit_category_id_product">Catégorie</label>
                    <select id="edit_category_id_product" name="category_id" required>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo htmlspecialchars($category['id']); ?>">
                                <?php echo htmlspecialchars($category['name_en']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>

                    <label for="edit_description_en">Description (Anglais)</label>
                    <textarea id="edit_description_en" name="description_en" required></textarea>

                    <label for="edit_description_ar">Description (Arabe)</label>
                    <textarea id="edit_description_ar" name="description_ar" required></textarea>

                    <label for="edit_product_image">Image du produit (Facultatif, max 5MB, tous formats d'image)</label>
                    <input type="file" id="edit_product_image" name="product_image" accept="image/*">

                    <button type="submit" name="update_product">Mettre à jour le produit</button>
                    <button type="button" class="cancel-edit-btn">Annuler</button>
                </form>
            </div>
        </div>

        <div class="admin-section">
            <h2>Ajouter une nouvelle catégorie</h2>
            <?php if (!empty($category_message)): ?>
                <p class="<?php echo strpos($category_message, 'Erreur') === false ? 'message' : 'error'; ?>" id="add-category-message">
                    <?php echo htmlspecialchars($category_message); ?>
                </p>
            <?php endif; ?>
            <form class="admin-form" method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>">
                <label for="category_name_en">Nom de la catégorie (Anglais)</label>
                <input type="text" id="category_name_en" name="category_name_en" required maxlength="255">

                <label for="category_name_ar">Nom de la catégorie (Arabe)</label>
                <input type="text" id="category_name_ar" name="category_name_ar" required maxlength="255">

                <button type="submit" name="add_category">Ajouter la catégorie</button>
            </form>
        </div>

        <div class="admin-section">
            <h2>Gérer les catégories</h2>
            <?php if (!empty($category_message)): ?>
                <p class="<?php echo strpos($category_message, 'Erreur') === false ? 'message' : 'error'; ?>" id="manage-category-message">
                    <?php echo htmlspecialchars($category_message); ?>
                </p>
            <?php endif; ?>
            <?php if (empty($categories)): ?>
                <p class="error">Aucune catégorie trouvée.</p>
            <?php else: ?>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nom (Anglais)</th>
                            <th>Nom (Arabe)</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($categories as $category): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($category['id']); ?></td>
                                <td><?php echo htmlspecialchars($category['name_en']); ?></td>
                                <td><?php echo htmlspecialchars($category['name_ar']); ?></td>
                                <td>
                                    <button class="action-btn edit-category-btn"
                                            data-id="<?php echo htmlspecialchars($category['id']); ?>"
                                            data-name-en="<?php echo htmlspecialchars($category['name_en']); ?>"
                                            data-name-ar="<?php echo htmlspecialchars($category['name_ar']); ?>">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <form method="POST" style="display:inline;" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ? Elle ne doit pas être associée à des produits.');">
                                        <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>">
                                        <input type="hidden" name="category_id" value="<?php echo htmlspecialchars($category['id']); ?>">
                                        <button type="submit" name="delete_category" class="action-btn">
                                            <span class="material-icons">delete</span>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>

            <div class="edit-form" id="edit-category-form">
                <h2>Modifier la catégorie</h2>
                <form class="admin-form" id="edit-category-form-element" method="POST" data-debug="edit-category">
                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>">
                    <input type="hidden" name="category_id" id="edit_category_id">
                    <label for="edit_category_name_en">Nom de la catégorie (Anglais)</label>
                    <input type="text" id="edit_category_name_en" name="category_name_en" required maxlength="255">

                    <label for="edit_category_name_ar">Nom de la catégorie (Arabe)</label>
                    <input type="text" id="edit_category_name_ar" name="category_name_ar" required maxlength="255">

                    <button type="submit" name="update_category">Mettre à jour la catégorie</button>
                    <button type="button" class="cancel-edit-btn">Annuler</button>
                </form>
            </div>
        </div>

        <!-- Cache Management Section -->
        <div class="admin-section">
            <h2>Cache Management</h2>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                    <div>
                        <strong>Current Mode:</strong><br>
                        <span style="color: <?php echo isDevelopmentMode() ? '#ff4444' : '#4CAF50'; ?>; font-weight: bold;">
                            <?php echo getAppMode(); ?>
                            <?php if (isDevelopmentMode()): ?>
                                <span style="background: #ff4444; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;">DEV</span>
                            <?php else: ?>
                                <span style="background: #4CAF50; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;">PROD</span>
                            <?php endif; ?>
                        </span>
                    </div>
                    <div>
                        <strong>Cache Status:</strong><br>
                        <span style="color: <?php echo CACHE_ENABLED ? '#4CAF50' : '#ff4444'; ?>;">
                            <?php echo CACHE_ENABLED ? 'Enabled' : 'Disabled'; ?>
                        </span>
                    </div>
                    <div>
                        <strong>Auto Versioning:</strong><br>
                        <span style="color: <?php echo AUTO_VERSION_FILES ? '#4CAF50' : '#666'; ?>;">
                            <?php echo AUTO_VERSION_FILES ? 'Enabled' : 'Disabled'; ?>
                        </span>
                    </div>
                    <div>
                        <strong>Translation Cache:</strong><br>
                        <span style="color: <?php echo TRANSLATION_CACHE_ENABLED ? '#4CAF50' : '#ff4444'; ?>;">
                            <?php echo TRANSLATION_CACHE_ENABLED ? 'Enabled' : 'Disabled'; ?>
                        </span>
                    </div>
                </div>

                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <button type="button" onclick="clearCache()" style="background: #D32F2F; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                        Clear All Cache
                    </button>
                    <a href="cache_manager.php" target="_blank" style="background: #666; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; display: inline-block;">
                        Advanced Cache Manager
                    </a>
                    <a href="clear_cache.php" target="_blank" style="background: #666; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; display: inline-block;">
                        Quick Clear Cache
                    </a>
                </div>

                <div id="cache-message" style="margin-top: 15px; padding: 10px; border-radius: 5px; display: none;"></div>
            </div>

            <div style="background: #e9ecef; padding: 15px; border-radius: 5px; font-size: 14px;">
                <strong>Development Mode Tips:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>CSS and JavaScript files will reload automatically when modified</li>
                    <li>Translation cache is disabled for real-time updates</li>
                    <li>Browser cache headers prevent caching</li>
                    <li>File versions use modification timestamps</li>
                </ul>
                <strong>Production Mode:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>All caching mechanisms are enabled for better performance</li>
                    <li>Static file versions prevent unnecessary reloads</li>
                    <li>Browser caching is allowed for faster loading</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const editProductButtons = document.querySelectorAll('.edit-product-btn');
            const editCategoryButtons = document.querySelectorAll('.edit-category-btn');
            const cancelEditButtons = document.querySelectorAll('.cancel-edit-btn');
            const editProductForm = document.querySelector('#edit-product-form');
            const editCategoryForm = document.querySelector('#edit-category-form');
            const addCategoryMessage = document.getElementById('add-category-message');
            const manageCategoryMessage = document.getElementById('manage-category-message');

            function clearCategoryMessages() {
                if (addCategoryMessage) addCategoryMessage.style.display = 'none';
                if (manageCategoryMessage) manageCategoryMessage.style.display = 'none';
            }

            editProductButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    clearCategoryMessages();
                    document.querySelector('#edit_product_id').value = btn.dataset.id;
                    document.querySelector('#edit_product_name').value = btn.dataset.name;
                    document.querySelector('#edit_category_id_product').value = btn.dataset.categoryId;
                    document.querySelector('#edit_description_en').value = btn.dataset.descEn;
                    document.querySelector('#edit_description_ar').value = btn.dataset.descAr;
                    document.querySelector('#edit_existing_image_path').value = btn.dataset.imagePath;
                    editProductForm.style.display = 'block';
                    editCategoryForm.style.display = 'none';
                });
            });

            editCategoryButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    clearCategoryMessages();
                    document.querySelector('#edit_category_id').value = btn.dataset.id;
                    document.querySelector('#edit_category_name_en').value = btn.dataset.nameEn;
                    document.querySelector('#edit_category_name_ar').value = btn.dataset.nameAr;
                    editCategoryForm.style.display = 'block';
                    editProductForm.style.display = 'none';
                });
            });

            cancelEditButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    editProductForm.style.display = 'none';
                    editCategoryForm.style.display = 'none';
                    clearCategoryMessages();
                    document.querySelector('#edit_category_id').value = '';
                    document.querySelector('#edit_category_name_en').value = '';
                    document.querySelector('#edit_category_name_ar').value = '';
                    document.querySelector('#edit_product_id').value = '';
                    document.querySelector('#edit_product_name').value = '';
                    document.querySelector('#edit_category_id_product').value = '';
                    document.querySelector('#edit_description_en').value = '';
                    document.querySelector('#edit_description_ar').value = '';
                    document.querySelector('#edit_existing_image_path').value = '';
                });
            });

            const editCategoryFormElement = document.querySelector('#edit-category-form-element');
            editCategoryFormElement.addEventListener('submit', (e) => {
                const id = document.querySelector('#edit_category_id').value;
                const nameEn = document.querySelector('#edit_category_name_en').value.trim();
                const nameAr = document.querySelector('#edit_category_name_ar').value.trim();

                if (!id || nameEn === '' || nameAr === '') {
                    e.preventDefault();
                    alert('Veuillez remplir tous les champs requis pour la catégorie.');
                    console.error('Échec de la validation du formulaire de catégorie :', { id, nameEn, nameAr });
                }
            });

            // Cache management functions
            window.clearCache = function() {
                const messageEl = document.getElementById('cache-message');
                messageEl.style.display = 'block';
                messageEl.style.background = '#fff3cd';
                messageEl.style.color = '#856404';
                messageEl.style.border = '1px solid #ffeaa7';
                messageEl.textContent = 'Clearing cache...';

                fetch('cache_manager.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=clear_cache'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        messageEl.style.background = '#d4edda';
                        messageEl.style.color = '#155724';
                        messageEl.style.border = '1px solid #c3e6cb';
                        messageEl.textContent = 'Cache cleared successfully!';
                    } else {
                        messageEl.style.background = '#f8d7da';
                        messageEl.style.color = '#721c24';
                        messageEl.style.border = '1px solid #f5c6cb';
                        messageEl.textContent = 'Error clearing cache: ' + data.message;
                    }

                    setTimeout(() => {
                        messageEl.style.display = 'none';
                    }, 5000);
                })
                .catch(error => {
                    messageEl.style.background = '#f8d7da';
                    messageEl.style.color = '#721c24';
                    messageEl.style.border = '1px solid #f5c6cb';
                    messageEl.textContent = 'Error: ' + error.message;

                    setTimeout(() => {
                        messageEl.style.display = 'none';
                    }, 5000);
                });
            };
        });
    </script>
</body>
</html>