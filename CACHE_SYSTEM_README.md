# Cache and File Versioning System

This document explains how to use the new caching and file versioning system implemented for the Madjour Industries web application.

## Quick Start

### Switching Between Development and Production Modes

1. **Development Mode** (for active development):
   - Edit `config.php`
   - Set `define('DEVELOPMENT_MODE', true);`
   - All caching is disabled
   - Files reload automatically when modified

2. **Production Mode** (for live site):
   - Edit `config.php`
   - Set `define('DEVELOPMENT_MODE', false);`
   - All caching is enabled
   - Static file versions are used

## Features

### Development Mode Benefits
- ✅ **Instant CSS/JS Updates**: Files reload automatically when modified
- ✅ **No Translation Cache**: Translations update immediately
- ✅ **No Browser Cache**: <PERSON>rows<PERSON> won't cache files
- ✅ **Automatic Versioning**: File versions use modification timestamps
- ✅ **Debug Logging**: Detailed logs for troubleshooting
- ✅ **Visual Indicator**: Red "DEV MODE" indicator on pages

### Production Mode Benefits
- ✅ **Optimized Performance**: All caching mechanisms enabled
- ✅ **Browser Caching**: Faster loading for returning visitors
- ✅ **Static Versioning**: Consistent file versions
- ✅ **Translation Caching**: Faster page loads
- ✅ **Minimal Logging**: Reduced server overhead

## File Structure

```
/
├── config.php              # Main configuration file
├── cache_manager.php       # Advanced cache management interface
├── clear_cache.php         # Simple cache clearing utility
├── test_cache_system.php   # System validation tests
├── index.php              # Updated with versioning system
├── translations.php       # Updated with cache controls
├── admin.php              # Updated with cache management
├── .htaccess              # Updated with cache headers
└── cache/                 # Cache directory (auto-created)
    ├── translations.json  # Translation cache file
    └── file_versions.json # File version cache
```

## Usage Instructions

### For Developers

1. **Start Development**:
   ```php
   // In config.php
   define('DEVELOPMENT_MODE', true);
   ```

2. **Make Changes**:
   - Edit CSS, JavaScript, or PHP files
   - Changes are visible immediately on page refresh
   - No manual cache clearing needed

3. **Clear Cache Manually** (if needed):
   - Visit `clear_cache.php` in browser
   - Use admin panel cache management
   - Run `php clear_cache.php` from command line

### For Production Deployment

1. **Switch to Production Mode**:
   ```php
   // In config.php
   define('DEVELOPMENT_MODE', false);
   ```

2. **Update Static Version** (when deploying new features):
   ```php
   // In config.php
   define('STATIC_VERSION', '1.3'); // Increment version
   ```

3. **Clear Cache After Deployment**:
   - Run cache clearing script
   - Ensures fresh content is served

## Cache Management Tools

### 1. Admin Panel Integration
- Access via admin panel
- Real-time cache status
- One-click cache clearing
- Mode switching (development only)

### 2. Advanced Cache Manager (`cache_manager.php`)
- Detailed cache file information
- File version monitoring
- Comprehensive cache controls
- System status overview

### 3. Quick Cache Clear (`clear_cache.php`)
- Simple cache clearing interface
- Command-line support
- Immediate results display
- Can be bookmarked for quick access

### 4. System Tests (`test_cache_system.php`)
- Validates entire system
- Checks configuration consistency
- Tests all functions
- Provides detailed diagnostics

## Configuration Options

### Main Settings (config.php)

```php
// Primary mode switch
define('DEVELOPMENT_MODE', true/false);

// File versioning
define('STATIC_VERSION', '1.2');
define('VERSIONED_FILES', ['styles.css', 'script.js', 'admin.js']);

// Cache settings (auto-configured based on mode)
define('CACHE_ENABLED', true/false);
define('CACHE_TTL', 3600);
define('AUTO_VERSION_FILES', true/false);
define('TRANSLATION_CACHE_ENABLED', true/false);
```

### Browser Cache Headers (.htaccess)

The system automatically configures appropriate cache headers:

- **Development**: `Cache-Control: no-cache, no-store, must-revalidate`
- **Production**: `Cache-Control: public, max-age=3600`

## Troubleshooting

### Common Issues

1. **Changes Not Visible**:
   - Check if in production mode
   - Clear browser cache manually
   - Run cache clearing script

2. **Cache Directory Errors**:
   - Ensure `cache/` directory is writable
   - Check file permissions (755 for directories, 644 for files)

3. **File Version Issues**:
   - Verify files exist in VERSIONED_FILES array
   - Check file modification times
   - Test with `test_cache_system.php`

### Debug Information

Enable debug logging:
```php
// In config.php (development mode enables this automatically)
define('DEBUG_ENABLED', true);
```

Check `debug.log` for detailed information about:
- Application startup
- Cache operations
- File versioning
- Translation loading

## Best Practices

### Development Workflow
1. Set development mode
2. Make changes to files
3. Refresh browser to see changes
4. No manual cache clearing needed

### Production Deployment
1. Test in development mode first
2. Switch to production mode
3. Increment static version number
4. Clear all caches
5. Test live site

### Performance Optimization
- Use production mode for live sites
- Monitor cache hit rates
- Clear cache only when necessary
- Keep static version numbers updated

## API Reference

### Utility Functions

```php
// Mode checking
isDevelopmentMode()     // Returns true if in development mode
isProductionMode()      // Returns true if in production mode
getAppMode()           // Returns 'development' or 'production'

// File versioning
getFileVersion($filename)    // Get version for specific file
getVersionedUrl($filename)   // Get complete URL with version

// Cache management
setCacheHeaders()           // Set appropriate HTTP headers
clearAllCaches()           // Clear all application caches
logApplicationContext()    // Log current configuration
```

## Support

For issues or questions:
1. Run `test_cache_system.php` to diagnose problems
2. Check `debug.log` for error messages
3. Use cache management tools to inspect system state
4. Verify configuration in `config.php`

---

**Note**: Always test changes in development mode before deploying to production!
