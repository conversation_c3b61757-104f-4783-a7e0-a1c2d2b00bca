<?php
/**
 * Cache System Test Script
 * 
 * This script tests all aspects of the caching and file versioning system
 * to ensure everything is working correctly.
 */

require_once 'config.php';

// Test results array
$tests = [];
$passed = 0;
$failed = 0;

/**
 * Add a test result
 */
function addTest($name, $result, $message = '') {
    global $tests, $passed, $failed;
    
    $tests[] = [
        'name' => $name,
        'result' => $result,
        'message' => $message
    ];
    
    if ($result) {
        $passed++;
    } else {
        $failed++;
    }
}

/**
 * Test configuration constants
 */
function testConfiguration() {
    addTest('DEVELOPMENT_MODE defined', defined('DEVELOPMENT_MODE'), 'Development mode constant should be defined');
    addTest('CACHE_ENABLED defined', defined('CACHE_ENABLED'), 'Cache enabled constant should be defined');
    addTest('AUTO_VERSION_FILES defined', defined('AUTO_VERSION_FILES'), 'Auto version files constant should be defined');
    addTest('VERSIONED_FILES defined', defined('VERSIONED_FILES'), 'Versioned files constant should be defined');
    
    // Test mode consistency
    if (DEVELOPMENT_MODE) {
        addTest('Dev mode cache disabled', !CACHE_ENABLED, 'Cache should be disabled in development mode');
        addTest('Dev mode auto versioning', AUTO_VERSION_FILES, 'Auto versioning should be enabled in development mode');
    } else {
        addTest('Prod mode cache enabled', CACHE_ENABLED, 'Cache should be enabled in production mode');
        addTest('Prod mode static versioning', !AUTO_VERSION_FILES, 'Static versioning should be used in production mode');
    }
}

/**
 * Test utility functions
 */
function testUtilityFunctions() {
    addTest('isDevelopmentMode() function', function_exists('isDevelopmentMode'), 'isDevelopmentMode function should exist');
    addTest('isProductionMode() function', function_exists('isProductionMode'), 'isProductionMode function should exist');
    addTest('getFileVersion() function', function_exists('getFileVersion'), 'getFileVersion function should exist');
    addTest('getVersionedUrl() function', function_exists('getVersionedUrl'), 'getVersionedUrl function should exist');
    addTest('clearAllCaches() function', function_exists('clearAllCaches'), 'clearAllCaches function should exist');
    
    // Test function logic
    if (function_exists('isDevelopmentMode') && function_exists('isProductionMode')) {
        $dev_result = isDevelopmentMode();
        $prod_result = isProductionMode();
        addTest('Mode functions consistency', $dev_result !== $prod_result, 'Development and production mode should be mutually exclusive');
    }
}

/**
 * Test file versioning
 */
function testFileVersioning() {
    if (function_exists('getFileVersion')) {
        // Test with existing files
        foreach (VERSIONED_FILES as $file) {
            if (file_exists($file)) {
                $version = getFileVersion($file);
                addTest("Version for {$file}", !empty($version), "Should return a version for {$file}");
                
                if (AUTO_VERSION_FILES) {
                    $expected_version = filemtime($file);
                    addTest("Timestamp version for {$file}", $version == $expected_version, "Should use file modification time in development mode");
                } else {
                    addTest("Static version for {$file}", $version == STATIC_VERSION, "Should use static version in production mode");
                }
            }
        }
        
        // Test versioned URL generation
        if (function_exists('getVersionedUrl')) {
            $test_file = 'styles.css';
            $versioned_url = getVersionedUrl($test_file);
            addTest('Versioned URL format', strpos($versioned_url, '?v=') !== false, 'Versioned URL should contain version parameter');
        }
    }
}

/**
 * Test cache directory and files
 */
function testCacheSystem() {
    // Test cache directory
    addTest('Cache directory exists', is_dir(CACHE_DIR), 'Cache directory should exist');
    addTest('Cache directory writable', is_writable(CACHE_DIR), 'Cache directory should be writable');
    
    // Test cache file constants
    addTest('Translation cache file defined', defined('TRANSLATION_CACHE_FILE'), 'Translation cache file constant should be defined');
    
    // Test cache clearing
    if (function_exists('clearAllCaches')) {
        $clear_result = clearAllCaches();
        addTest('Cache clearing function', is_array($clear_result), 'clearAllCaches should return an array');
    }
}

/**
 * Test translation system integration
 */
function testTranslationSystem() {
    if (file_exists('translations.php')) {
        addTest('Translations file exists', true, 'translations.php file should exist');
        
        // Test if translations.php includes config
        $translations_content = file_get_contents('translations.php');
        addTest('Translations includes config', strpos($translations_content, 'config.php') !== false, 'translations.php should include config.php');
        addTest('Translations uses cache settings', strpos($translations_content, 'TRANSLATION_CACHE_ENABLED') !== false, 'translations.php should use cache settings from config');
    }
}

/**
 * Test admin integration
 */
function testAdminIntegration() {
    if (file_exists('admin.php')) {
        addTest('Admin file exists', true, 'admin.php file should exist');
        
        $admin_content = file_get_contents('admin.php');
        addTest('Admin includes config', strpos($admin_content, 'config.php') !== false, 'admin.php should include config.php');
        addTest('Admin has cache management', strpos($admin_content, 'Cache Management') !== false, 'admin.php should have cache management section');
    }
}

/**
 * Test index.php integration
 */
function testIndexIntegration() {
    if (file_exists('index.php')) {
        addTest('Index file exists', true, 'index.php file should exist');
        
        $index_content = file_get_contents('index.php');
        addTest('Index includes config', strpos($index_content, 'config.php') !== false, 'index.php should include config.php');
        addTest('Index uses versioned URLs', strpos($index_content, 'getVersionedUrl') !== false, 'index.php should use versioned URLs');
    }
}

// Run all tests
testConfiguration();
testUtilityFunctions();
testFileVersioning();
testCacheSystem();
testTranslationSystem();
testAdminIntegration();
testIndexIntegration();

// Output results
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache System Test Results</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #D32F2F;
            text-align: center;
            margin-bottom: 30px;
        }
        .summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
            text-align: center;
        }
        .summary.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .summary.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .summary.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-result {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .test-result.pass {
            background: #d4edda;
            color: #155724;
        }
        .test-result.fail {
            background: #f8d7da;
            color: #721c24;
        }
        .test-name {
            font-weight: bold;
        }
        .test-status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .test-status.pass {
            background: #28a745;
            color: white;
        }
        .test-status.fail {
            background: #dc3545;
            color: white;
        }
        .test-message {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .config-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .actions {
            text-align: center;
            margin-top: 30px;
        }
        .btn {
            background: #D32F2F;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 5px;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #B71C1C;
        }
        .btn.secondary {
            background: #666;
        }
        .btn.secondary:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cache System Test Results</h1>
        
        <?php
        $total = $passed + $failed;
        $success_rate = $total > 0 ? ($passed / $total) * 100 : 0;
        
        if ($failed === 0) {
            $summary_class = 'success';
            $summary_text = 'All tests passed! ✓';
        } elseif ($success_rate >= 80) {
            $summary_class = 'warning';
            $summary_text = 'Most tests passed with some issues';
        } else {
            $summary_class = 'error';
            $summary_text = 'Multiple test failures detected';
        }
        ?>
        
        <div class="summary <?php echo $summary_class; ?>">
            <h2><?php echo $summary_text; ?></h2>
            <p><strong><?php echo $passed; ?></strong> passed, <strong><?php echo $failed; ?></strong> failed out of <strong><?php echo $total; ?></strong> tests</p>
            <p>Success rate: <strong><?php echo number_format($success_rate, 1); ?>%</strong></p>
        </div>
        
        <div class="config-info">
            <h3>Current Configuration</h3>
            <p><strong>Mode:</strong> <?php echo getAppMode(); ?></p>
            <p><strong>Cache Enabled:</strong> <?php echo CACHE_ENABLED ? 'Yes' : 'No'; ?></p>
            <p><strong>Auto File Versioning:</strong> <?php echo AUTO_VERSION_FILES ? 'Yes' : 'No'; ?></p>
            <p><strong>Translation Cache:</strong> <?php echo TRANSLATION_CACHE_ENABLED ? 'Yes' : 'No'; ?></p>
            <p><strong>Debug Enabled:</strong> <?php echo DEBUG_ENABLED ? 'Yes' : 'No'; ?></p>
        </div>
        
        <h2>Test Details</h2>
        <?php foreach ($tests as $test): ?>
            <div class="test-result <?php echo $test['result'] ? 'pass' : 'fail'; ?>">
                <div>
                    <div class="test-name"><?php echo htmlspecialchars($test['name']); ?></div>
                    <?php if (!empty($test['message'])): ?>
                        <div class="test-message"><?php echo htmlspecialchars($test['message']); ?></div>
                    <?php endif; ?>
                </div>
                <div class="test-status <?php echo $test['result'] ? 'pass' : 'fail'; ?>">
                    <?php echo $test['result'] ? 'PASS' : 'FAIL'; ?>
                </div>
            </div>
        <?php endforeach; ?>
        
        <div class="actions">
            <a href="index.php" class="btn">Back to Site</a>
            <a href="cache_manager.php" class="btn secondary">Cache Manager</a>
            <a href="admin.php" class="btn secondary">Admin Panel</a>
            <a href="test_cache_system.php" class="btn secondary">Run Tests Again</a>
        </div>
    </div>
</body>
</html>
