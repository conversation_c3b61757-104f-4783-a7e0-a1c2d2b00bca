<?php
/**
 * Application Configuration
 * 
 * This file manages development/production modes, caching settings,
 * and file versioning for the Madjour Industries application.
 * 
 * To switch between development and production modes, simply change
 * the DEVELOPMENT_MODE constant below.
 */

// =============================================================================
// MAIN CONFIGURATION SWITCH
// =============================================================================

/**
 * Set to true for development mode, false for production mode
 * 
 * Development Mode Features:
 * - Disables all caching mechanisms
 * - Forces file reloading with timestamps
 * - Enables debug logging
 * - Shows detailed error messages
 * 
 * Production Mode Features:
 * - Enables caching for better performance
 * - Uses static file versions
 * - Minimal error reporting
 * - Optimized for speed
 */
define('DEVELOPMENT_MODE', true);

// =============================================================================
// ENVIRONMENT-SPECIFIC SETTINGS
// =============================================================================

if (DEVELOPMENT_MODE) {
    // Development Mode Settings
    define('CACHE_ENABLED', false);
    define('CACHE_TTL', 0);
    define('AUTO_VERSION_FILES', true);
    define('DEBUG_ENABLED', true);
    define('ERROR_REPORTING_LEVEL', E_ALL);
    define('DISPLAY_ERRORS', true);
    define('LOG_ERRORS', true);
    
    // Force no-cache headers in development
    define('FORCE_NO_CACHE', true);
    
    // Translation cache disabled in development
    define('TRANSLATION_CACHE_ENABLED', false);
    define('TRANSLATION_CACHE_TTL', 0);
    
} else {
    // Production Mode Settings
    define('CACHE_ENABLED', true);
    define('CACHE_TTL', 3600); // 1 hour
    define('AUTO_VERSION_FILES', false);
    define('DEBUG_ENABLED', false);
    define('ERROR_REPORTING_LEVEL', E_ERROR | E_WARNING);
    define('DISPLAY_ERRORS', false);
    define('LOG_ERRORS', true);
    
    // Allow browser caching in production
    define('FORCE_NO_CACHE', false);
    
    // Translation cache enabled in production
    define('TRANSLATION_CACHE_ENABLED', true);
    define('TRANSLATION_CACHE_TTL', 3600); // 1 hour
}

// =============================================================================
// FILE VERSIONING CONFIGURATION
// =============================================================================

/**
 * Static version number for production use
 * Increment this manually when deploying new versions in production
 */
define('STATIC_VERSION', '1.2');

/**
 * Files to apply versioning to
 */
define('VERSIONED_FILES', [
    'styles.css',
    'script.js',
    'admin.js'
]);

// =============================================================================
// CACHE DIRECTORIES AND FILES
// =============================================================================

define('CACHE_DIR', __DIR__ . '/cache');
define('TRANSLATION_CACHE_FILE', CACHE_DIR . '/translations.json');
define('FILE_VERSION_CACHE', CACHE_DIR . '/file_versions.json');

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Get the current application mode as a string
 */
function getAppMode() {
    return DEVELOPMENT_MODE ? 'development' : 'production';
}

/**
 * Check if we're in development mode
 */
function isDevelopmentMode() {
    return DEVELOPMENT_MODE === true;
}

/**
 * Check if we're in production mode
 */
function isProductionMode() {
    return DEVELOPMENT_MODE === false;
}

/**
 * Get file version for cache busting
 * 
 * @param string $filename The filename to get version for
 * @return string Version string to append to file URL
 */
function getFileVersion($filename) {
    if (!in_array($filename, VERSIONED_FILES)) {
        return STATIC_VERSION;
    }
    
    if (AUTO_VERSION_FILES && isDevelopmentMode()) {
        // In development, use file modification time for instant updates
        $filepath = __DIR__ . '/' . $filename;
        if (file_exists($filepath)) {
            return filemtime($filepath);
        }
        return time(); // Fallback to current time
    } else {
        // In production, use static version
        return STATIC_VERSION;
    }
}

/**
 * Generate versioned URL for CSS/JS files
 * 
 * @param string $filename The filename
 * @return string Complete URL with version parameter
 */
function getVersionedUrl($filename) {
    $version = getFileVersion($filename);
    return $filename . '?v=' . $version;
}

/**
 * Set appropriate cache headers based on current mode
 */
function setCacheHeaders() {
    if (FORCE_NO_CACHE || isDevelopmentMode()) {
        // Development mode: prevent all caching
        header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0');
        header('Pragma: no-cache');
        header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');
        
        // Set environment variable for .htaccess
        $_SERVER['NO_CACHE'] = '1';
        
    } else {
        // Production mode: allow reasonable caching
        header('Cache-Control: public, max-age=3600'); // 1 hour
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
    }
    
    // Always set UTF-8 and security headers
    header('Content-Type: text/html; charset=UTF-8');
    header('X-Content-Type-Options: nosniff');
}

/**
 * Log application context for debugging
 */
function logApplicationContext() {
    if (DEBUG_ENABLED) {
        $context = [
            'dev_mode' => isDevelopmentMode(),
            'cache_enabled' => CACHE_ENABLED,
            'cache_ttl' => CACHE_TTL,
            'auto_version' => AUTO_VERSION_FILES,
            'debug_enabled' => DEBUG_ENABLED,
            'php_version' => PHP_VERSION,
            'server_time' => date('Y-m-d H:i:s')
        ];
        
        $log_message = '[' . date('Y-m-d H:i:s') . '] Application started | Context: ' . json_encode($context) . "\n";
        file_put_contents('debug.log', $log_message, FILE_APPEND | LOCK_EX);
    }
}

/**
 * Clear all application caches
 * 
 * @return array Results of cache clearing operations
 */
function clearAllCaches() {
    $results = [];
    
    // Clear translation cache
    if (file_exists(TRANSLATION_CACHE_FILE)) {
        $results['translations'] = unlink(TRANSLATION_CACHE_FILE);
    } else {
        $results['translations'] = true; // Already cleared
    }
    
    // Clear file version cache
    if (file_exists(FILE_VERSION_CACHE)) {
        $results['file_versions'] = unlink(FILE_VERSION_CACHE);
    } else {
        $results['file_versions'] = true; // Already cleared
    }
    
    // Clear any other cache files in cache directory
    $cache_files = glob(CACHE_DIR . '/*.json');
    $results['other_files'] = [];
    foreach ($cache_files as $file) {
        if (!in_array($file, [TRANSLATION_CACHE_FILE, FILE_VERSION_CACHE])) {
            $results['other_files'][basename($file)] = unlink($file);
        }
    }
    
    return $results;
}

// =============================================================================
// INITIALIZATION
// =============================================================================

// Set error reporting based on mode
error_reporting(ERROR_REPORTING_LEVEL);
ini_set('display_errors', DISPLAY_ERRORS ? '1' : '0');
ini_set('log_errors', LOG_ERRORS ? '1' : '0');

// Ensure cache directory exists
if (!is_dir(CACHE_DIR)) {
    mkdir(CACHE_DIR, 0755, true);
}

// Log application startup context
logApplicationContext();

?>
