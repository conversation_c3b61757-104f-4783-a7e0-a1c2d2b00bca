<?php
/**
 * Simple Cache Clearing Script
 * 
 * This script can be used to quickly clear all application caches.
 * Can be run from command line or included in other scripts.
 * 
 * Usage:
 * - Command line: php clear_cache.php
 * - Web browser: http://yoursite.com/clear_cache.php
 * - Include in script: require_once 'clear_cache.php';
 */

require_once 'config.php';

// Function to clear cache and return results
function performCacheClear() {
    $results = clearAllCaches();
    
    $success_count = 0;
    $total_count = 0;
    $messages = [];
    
    // Check translation cache result
    if (isset($results['translations'])) {
        $total_count++;
        if ($results['translations']) {
            $success_count++;
            $messages[] = "✓ Translation cache cleared";
        } else {
            $messages[] = "✗ Failed to clear translation cache";
        }
    }
    
    // Check file version cache result
    if (isset($results['file_versions'])) {
        $total_count++;
        if ($results['file_versions']) {
            $success_count++;
            $messages[] = "✓ File version cache cleared";
        } else {
            $messages[] = "✗ Failed to clear file version cache";
        }
    }
    
    // Check other files
    if (isset($results['other_files']) && is_array($results['other_files'])) {
        foreach ($results['other_files'] as $file => $result) {
            $total_count++;
            if ($result) {
                $success_count++;
                $messages[] = "✓ Cleared cache file: {$file}";
            } else {
                $messages[] = "✗ Failed to clear cache file: {$file}";
            }
        }
    }
    
    return [
        'success' => $success_count === $total_count,
        'cleared' => $success_count,
        'total' => $total_count,
        'messages' => $messages,
        'details' => $results
    ];
}

// Check if this is being run from command line
$is_cli = php_sapi_name() === 'cli';

// Check if this is a direct web request (not an include)
$is_direct_request = !$is_cli && basename($_SERVER['PHP_SELF']) === 'clear_cache.php';

// Perform cache clearing if this is a direct request
if ($is_cli || $is_direct_request) {
    $result = performCacheClear();
    
    if ($is_cli) {
        // Command line output
        echo "Cache Clearing Results:\n";
        echo "======================\n";
        echo "Mode: " . getAppMode() . "\n";
        echo "Cleared: {$result['cleared']}/{$result['total']} items\n";
        echo "Status: " . ($result['success'] ? 'SUCCESS' : 'PARTIAL/FAILED') . "\n\n";
        
        foreach ($result['messages'] as $message) {
            echo $message . "\n";
        }
        
        if (DEBUG_ENABLED) {
            echo "\nDebug Details:\n";
            print_r($result['details']);
        }
        
    } else {
        // Web output
        header('Content-Type: text/html; charset=UTF-8');
        ?>
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Cache Cleared - Madjour Industries</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }
                .container {
                    max-width: 600px;
                    margin: 0 auto;
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                h1 {
                    color: #D32F2F;
                    text-align: center;
                    margin-bottom: 30px;
                }
                .status {
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                    text-align: center;
                    font-size: 18px;
                    font-weight: bold;
                }
                .status.success {
                    background: #d4edda;
                    color: #155724;
                    border: 1px solid #c3e6cb;
                }
                .status.partial {
                    background: #fff3cd;
                    color: #856404;
                    border: 1px solid #ffeaa7;
                }
                .messages {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }
                .message {
                    margin: 5px 0;
                    padding: 5px 0;
                }
                .message.success {
                    color: #155724;
                }
                .message.error {
                    color: #721c24;
                }
                .info {
                    background: #e9ecef;
                    padding: 15px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }
                .actions {
                    text-align: center;
                }
                .btn {
                    background: #D32F2F;
                    color: white;
                    text-decoration: none;
                    padding: 12px 24px;
                    border-radius: 5px;
                    display: inline-block;
                    margin: 5px;
                }
                .btn:hover {
                    background: #B71C1C;
                }
                .btn.secondary {
                    background: #666;
                }
                .btn.secondary:hover {
                    background: #555;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Cache Clearing Results</h1>
                
                <div class="status <?php echo $result['success'] ? 'success' : 'partial'; ?>">
                    <?php if ($result['success']): ?>
                        ✓ All caches cleared successfully!
                    <?php else: ?>
                        ⚠ Cache clearing completed with issues
                    <?php endif; ?>
                </div>
                
                <div class="info">
                    <strong>Mode:</strong> <?php echo getAppMode(); ?><br>
                    <strong>Items processed:</strong> <?php echo $result['cleared']; ?>/<?php echo $result['total']; ?><br>
                    <strong>Time:</strong> <?php echo date('Y-m-d H:i:s'); ?>
                </div>
                
                <div class="messages">
                    <h3>Details:</h3>
                    <?php foreach ($result['messages'] as $message): ?>
                        <div class="message <?php echo strpos($message, '✓') === 0 ? 'success' : 'error'; ?>">
                            <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="actions">
                    <a href="index.php" class="btn">Back to Site</a>
                    <a href="cache_manager.php" class="btn secondary">Cache Manager</a>
                    <a href="clear_cache.php" class="btn secondary">Clear Again</a>
                </div>
                
                <?php if (DEBUG_ENABLED && !empty($result['details'])): ?>
                <div class="messages">
                    <h3>Debug Information:</h3>
                    <pre><?php print_r($result['details']); ?></pre>
                </div>
                <?php endif; ?>
            </div>
        </body>
        </html>
        <?php
    }
    
    // Log the cache clearing operation
    if (DEBUG_ENABLED) {
        $log_message = '[' . date('Y-m-d H:i:s') . '] Cache cleared manually | Results: ' . json_encode($result) . "\n";
        file_put_contents('debug.log', $log_message, FILE_APPEND | LOCK_EX);
    }
}

// If this file is included (not run directly), just make the function available
// The performCacheClear() function can be called from other scripts
?>
