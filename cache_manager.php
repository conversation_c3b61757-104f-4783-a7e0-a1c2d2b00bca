<?php
/**
 * Cache Management Utility
 * 
 * This file provides tools for managing application cache,
 * including manual cache clearing and cache status monitoring.
 */

require_once 'config.php';

// Check if this is an AJAX request for cache operations
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'clear_cache':
            $results = clearAllCaches();
            echo json_encode([
                'success' => true,
                'message' => 'Cache cleared successfully',
                'details' => $results
            ]);
            exit;
            
        case 'get_cache_status':
            $status = getCacheStatus();
            echo json_encode([
                'success' => true,
                'status' => $status
            ]);
            exit;
            
        case 'toggle_dev_mode':
            $result = toggleDevelopmentMode();
            echo json_encode($result);
            exit;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => 'Unknown action'
            ]);
            exit;
    }
}

/**
 * Get current cache status
 */
function getCacheStatus() {
    $status = [
        'development_mode' => isDevelopmentMode(),
        'cache_enabled' => CACHE_ENABLED,
        'translation_cache_enabled' => TRANSLATION_CACHE_ENABLED,
        'auto_version_files' => AUTO_VERSION_FILES,
        'cache_files' => []
    ];
    
    // Check translation cache
    if (file_exists(TRANSLATION_CACHE_FILE)) {
        $status['cache_files']['translations'] = [
            'exists' => true,
            'size' => filesize(TRANSLATION_CACHE_FILE),
            'modified' => date('Y-m-d H:i:s', filemtime(TRANSLATION_CACHE_FILE)),
            'age_seconds' => time() - filemtime(TRANSLATION_CACHE_FILE)
        ];
    } else {
        $status['cache_files']['translations'] = ['exists' => false];
    }
    
    // Check other cache files
    $cache_files = glob(CACHE_DIR . '/*.json');
    foreach ($cache_files as $file) {
        if ($file !== TRANSLATION_CACHE_FILE) {
            $name = basename($file, '.json');
            $status['cache_files'][$name] = [
                'exists' => true,
                'size' => filesize($file),
                'modified' => date('Y-m-d H:i:s', filemtime($file)),
                'age_seconds' => time() - filemtime($file)
            ];
        }
    }
    
    // Get file versions
    $status['file_versions'] = [];
    foreach (VERSIONED_FILES as $file) {
        $status['file_versions'][$file] = getFileVersion($file);
    }
    
    return $status;
}

/**
 * Toggle development mode (for demonstration - in real use, edit config.php manually)
 */
function toggleDevelopmentMode() {
    // Note: This is a demonstration function. In production, you should
    // manually edit the config.php file to change DEVELOPMENT_MODE
    
    $config_file = __DIR__ . '/config.php';
    $config_content = file_get_contents($config_file);
    
    if (strpos($config_content, "define('DEVELOPMENT_MODE', true);") !== false) {
        $new_content = str_replace(
            "define('DEVELOPMENT_MODE', true);",
            "define('DEVELOPMENT_MODE', false);",
            $config_content
        );
        $new_mode = 'production';
    } else {
        $new_content = str_replace(
            "define('DEVELOPMENT_MODE', false);",
            "define('DEVELOPMENT_MODE', true);",
            $config_content
        );
        $new_mode = 'development';
    }
    
    if (file_put_contents($config_file, $new_content)) {
        // Clear cache after mode change
        clearAllCaches();
        
        return [
            'success' => true,
            'message' => "Switched to {$new_mode} mode",
            'new_mode' => $new_mode
        ];
    } else {
        return [
            'success' => false,
            'message' => 'Failed to update configuration file'
        ];
    }
}

/**
 * Format file size for display
 */
function formatFileSize($bytes) {
    if ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

/**
 * Format time duration for display
 */
function formatDuration($seconds) {
    if ($seconds >= 3600) {
        return number_format($seconds / 3600, 1) . ' hours';
    } elseif ($seconds >= 60) {
        return number_format($seconds / 60, 1) . ' minutes';
    } else {
        return $seconds . ' seconds';
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache Manager - Madjour Industries</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #D32F2F;
            margin-bottom: 30px;
            text-align: center;
        }
        .status-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 3px;
        }
        .status-label {
            font-weight: bold;
        }
        .status-value {
            color: #666;
        }
        .status-value.enabled {
            color: #4CAF50;
        }
        .status-value.disabled {
            color: #f44336;
        }
        .btn {
            background: #D32F2F;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .btn:hover {
            background: #B71C1C;
        }
        .btn.secondary {
            background: #666;
        }
        .btn.secondary:hover {
            background: #555;
        }
        .message {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            display: none;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .cache-files {
            margin-top: 20px;
        }
        .cache-file {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #D32F2F;
        }
        .file-versions {
            margin-top: 20px;
        }
        .version-item {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            display: flex;
            justify-content: space-between;
        }
        .dev-indicator {
            background: #ff4444;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            display: inline-block;
            margin-left: 10px;
        }
        .prod-indicator {
            background: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            display: inline-block;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cache Manager</h1>
        
        <div id="message" class="message"></div>
        
        <div class="status-section">
            <h2>Current Status</h2>
            <div id="status-content">
                <div class="status-item">
                    <span class="status-label">Mode:</span>
                    <span class="status-value">
                        <?php echo getAppMode(); ?>
                        <?php if (isDevelopmentMode()): ?>
                            <span class="dev-indicator">DEV</span>
                        <?php else: ?>
                            <span class="prod-indicator">PROD</span>
                        <?php endif; ?>
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">Cache Enabled:</span>
                    <span class="status-value <?php echo CACHE_ENABLED ? 'enabled' : 'disabled'; ?>">
                        <?php echo CACHE_ENABLED ? 'Yes' : 'No'; ?>
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">Auto File Versioning:</span>
                    <span class="status-value <?php echo AUTO_VERSION_FILES ? 'enabled' : 'disabled'; ?>">
                        <?php echo AUTO_VERSION_FILES ? 'Yes' : 'No'; ?>
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">Translation Cache:</span>
                    <span class="status-value <?php echo TRANSLATION_CACHE_ENABLED ? 'enabled' : 'disabled'; ?>">
                        <?php echo TRANSLATION_CACHE_ENABLED ? 'Yes' : 'No'; ?>
                    </span>
                </div>
            </div>
        </div>
        
        <div class="status-section">
            <h2>Actions</h2>
            <button class="btn" onclick="clearCache()">Clear All Cache</button>
            <button class="btn secondary" onclick="refreshStatus()">Refresh Status</button>
            <button class="btn secondary" onclick="toggleDevMode()">Toggle Dev/Prod Mode</button>
        </div>
        
        <div class="status-section">
            <h2>Cache Files</h2>
            <div id="cache-files-content">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
        
        <div class="status-section">
            <h2>File Versions</h2>
            <div id="file-versions-content">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script>
        function showMessage(text, type = 'success') {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';
            
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 5000);
        }

        function clearCache() {
            fetch('cache_manager.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=clear_cache'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    refreshStatus();
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('Error clearing cache: ' + error.message, 'error');
            });
        }

        function toggleDevMode() {
            if (confirm('This will switch between development and production mode. Continue?')) {
                fetch('cache_manager.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=toggle_dev_mode'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } else {
                        showMessage(data.message, 'error');
                    }
                })
                .catch(error => {
                    showMessage('Error toggling mode: ' + error.message, 'error');
                });
            }
        }

        function refreshStatus() {
            fetch('cache_manager.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_cache_status'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateCacheFilesDisplay(data.status.cache_files);
                    updateFileVersionsDisplay(data.status.file_versions);
                }
            })
            .catch(error => {
                showMessage('Error refreshing status: ' + error.message, 'error');
            });
        }

        function updateCacheFilesDisplay(cacheFiles) {
            const container = document.getElementById('cache-files-content');
            container.innerHTML = '';
            
            for (const [name, info] of Object.entries(cacheFiles)) {
                if (info.exists) {
                    const div = document.createElement('div');
                    div.className = 'cache-file';
                    div.innerHTML = `
                        <strong>${name}</strong><br>
                        Size: ${formatFileSize(info.size)}<br>
                        Modified: ${info.modified}<br>
                        Age: ${formatDuration(info.age_seconds)}
                    `;
                    container.appendChild(div);
                } else {
                    const div = document.createElement('div');
                    div.className = 'cache-file';
                    div.innerHTML = `<strong>${name}</strong><br><em>No cache file</em>`;
                    container.appendChild(div);
                }
            }
        }

        function updateFileVersionsDisplay(fileVersions) {
            const container = document.getElementById('file-versions-content');
            container.innerHTML = '';
            
            for (const [file, version] of Object.entries(fileVersions)) {
                const div = document.createElement('div');
                div.className = 'version-item';
                div.innerHTML = `
                    <span><strong>${file}</strong></span>
                    <span>v${version}</span>
                `;
                container.appendChild(div);
            }
        }

        function formatFileSize(bytes) {
            if (bytes >= 1048576) {
                return (bytes / 1048576).toFixed(2) + ' MB';
            } else if (bytes >= 1024) {
                return (bytes / 1024).toFixed(2) + ' KB';
            } else {
                return bytes + ' bytes';
            }
        }

        function formatDuration(seconds) {
            if (seconds >= 3600) {
                return (seconds / 3600).toFixed(1) + ' hours';
            } else if (seconds >= 60) {
                return (seconds / 60).toFixed(1) + ' minutes';
            } else {
                return seconds + ' seconds';
            }
        }

        // Load initial status
        refreshStatus();
    </script>
</body>
</html>
