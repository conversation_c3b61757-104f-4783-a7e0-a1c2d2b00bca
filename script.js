// Current language state
let currentLang = 'ar';

// Translation data (ensure this matches your actual translations)
const translations = {
  en: {
    'nav.home': 'samah',
    'nav.categories': 'Categories',
    'nav.products': 'Products',
    'nav.about': 'About Us',
    'nav.contact': 'Contact',
    'hero.title': 'Trusted Products for Everyday Life',
    'hero.subtitle': 'Madjour Industries Quality You Can Count On',
    'categories.title': 'Categories',
    'categories.lighters_gas': 'Lighters & Gas',
    'categories.toothbrushes': 'Toothbrushes',
    'categories.adhesive_glue': 'Adhesive Glue',
    'categories.baby_products': 'Baby Products',
    'categories.insecticides': 'Insecticides',
    'products.title': 'Our Products',
    'products.view_details': 'View Details',
    'search.placeholder': 'Search products...',
    'search.empty': 'Please enter a search term.',
    'search.no_results': 'No products found for "%s".',
    'about.title': 'About EURL MADJOUR HICHEM',
    'about.description': 'EURL MADJOUR HICHEM is a leading company in multiple sectors, comprising several manufacturing units, including lighters, gas, glue, insecticides, and semi-pharmaceutical materials.',
    'about.feature1': 'Lighters Manufacturing',
    'about.feature1_desc': 'High-quality lighter production with strict quality control',
    'about.feature2': 'Gas Production',
    'about.feature2_desc': 'Safe and efficient gasonzeppa gas manufacturing solutions',
    'about.feature3': 'Industrial Materials',
    'about.feature3_desc': 'Production of glue, insecticides, and semi-pharmaceuticals',
    'footer.quick_links': 'Quick Links',
    'footer.contact_title': 'Contact Info',
    'footer.rights': 'All rights reserved.',
    'modal.close': 'Close',
    'modal.close_aria': 'Close Modal',
    'mobile.menu_toggle': 'Toggle Menu'
  },
  ar: {
    'nav.home': 'الرئيسية',
    'nav.categories': 'الفئات',
    'nav.products': 'المنتجات',
    'nav.about': 'من نحن',
    'nav.contact': 'اتصل بنا',
    'hero.title': 'منتجات موثوقة للحياة اليومية',
    'hero.subtitle': 'صناعات مجور - جودة يمكنك الاعتماد عليها',
    'categories.title': 'الفئات',
    'categories.lighters_gas': 'الولاعات والغاز',
    'categories.toothbrushes': 'فرش الأسنان',
    'categories.adhesive_glue': 'الغراء اللاصق',
    'categories.baby_products': 'منتجات الأطفال',
    'categories.insecticides': 'المبيدات الحشرية',
    'products.title': 'منتجاتنا',
    'products.view_details': 'عرض التفاصيل',
    'search.placeholder': 'البحث عن المنتجات...',
    'search.empty': 'يرجى إدخال مصطلح البحث.',
    'search.no_results': 'لم يتم العثور على منتجات لـ "%s".',
    'about.title': 'حول شركة EURL MADJOUR HICHEM',
    'about.description': 'شركة EURL MADJOUR HICHEM هي شركة رائدة في عدة قطاعات، تضم عدة وحدات تصنيع، بما في ذلك الولاعات والغاز والغراء والمبيدات الحشرية والمواد شبه الصيدلانية.',
    'about.feature1': 'تصنيع الولاعات',
    'about.feature1_desc': 'إنتاج ولاعات عالية الجودة مع مراقبة جودة صارمة',
    'about.feature2': 'إنتاج الغاز',
    'about.feature2_desc': 'حلول تصنيع غاز gasonzeppa آمنة وفعالة',
    'about.feature3': 'المواد الصناعية',
    'about.feature3_desc': 'إنتاج الغراء والمبيدات الحشرية والمنتجات شبه الصيدلانية',
    'footer.quick_links': 'روابط سريعة',
    'footer.contact_title': 'معلومات الاتصال',
    'footer.rights': 'جميع الحقوق محفوظة.',
    'modal.close': 'إغلاق',
    'modal.close_aria': 'إغلاق النافذة',
    'mobile.menu_toggle': 'تبديل القائمة'
  }
};

// DOM elements (cached for performance)
const elements = {
  langButtons: document.querySelectorAll('.lang-btn'),
  searchBar: document.querySelector('.search-bar'),
  searchBtn: document.querySelector('.search-btn'),
  mobileMenuBtn: document.querySelector('.mobile-menu-btn'),
  navMenu: document.querySelector('.nav-menu'),
  navLinks: document.querySelectorAll('.nav-link'),
  categoryTabs: document.querySelectorAll('.category-tab'),
  productModal: document.getElementById('product-modal'),
  modalCloseButtons: document.querySelectorAll('.modal-close-btn'),
  modalTitle: document.querySelector('.modal-title'),
  modalImage: document.querySelector('.modal-product-image'),
  modalDescription: document.querySelector('.modal-description'),
  aboutTitle: document.querySelector('.about-title'),
  aboutDescription: document.querySelector('.about-description'),
  aboutFeatures: document.querySelectorAll('.feature h3'),
  aboutFeatureDescriptions: document.querySelectorAll('.feature p'),
  body: document.body
};

// Initialize the application
function init() {
  // Re-select language buttons to include mobile menu buttons
  elements.langButtons = document.querySelectorAll('.lang-btn');

  if (!Object.keys(elements).every(key => elements[key] || key === 'modalDescription')) {
    console.error('Missing critical DOM elements. Application initialization aborted.');
    return;
  }

  setupEventListeners();
  setupIntersectionObserver();
  updateLanguage(currentLang);
}

// Setup all event listeners
function setupEventListeners() {
  // Language switcher
  elements.langButtons.forEach(btn => btn.addEventListener('click', handleLanguageSwitch));

  // Search functionality
  if (elements.searchBtn && elements.searchBar) {
    elements.searchBtn.addEventListener('click', handleSearch);
    elements.searchBar.addEventListener('keypress', e => e.key === 'Enter' && handleSearch());
  }

  // Mobile menu toggle
  if (elements.mobileMenuBtn && elements.navMenu) {
    elements.mobileMenuBtn.addEventListener('click', toggleMobileMenu);
  }

  // Navigation links
  elements.navLinks.forEach(link => link.addEventListener('click', handleNavClick));

  // Category tabs
  elements.categoryTabs.forEach(tab => {
    tab.addEventListener('click', handleCategoryTabClick);
    tab.addEventListener('keypress', e => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        handleCategoryTabClick.call(tab);
      }
    });
  });

  // View details buttons (delegated event listener)
  document.addEventListener('click', e => {
    if (e.target.classList.contains('view-details-btn')) handleViewDetails(e);
  });

  // Modal close buttons
  elements.modalCloseButtons.forEach(btn => btn.addEventListener('click', closeProductModal));

  // Close modal with Escape key
  document.addEventListener('keydown', e => {
    if (e.key === 'Escape' && elements.productModal?.classList.contains('active')) {
      closeProductModal();
    }
  });

  // Trap focus in modal
  if (elements.productModal) {
    elements.productModal.addEventListener('keydown', handleModalFocusTrap);
  }

  // Debounced resize handler
  window.addEventListener('resize', debounce(handleResize, 100));

  // Keyboard navigation
  document.addEventListener('keydown', handleKeyboardNavigation);
}

// Language switching functionality
function handleLanguageSwitch(e) {
  const newLang = e.target.dataset.lang;
  if (!newLang || newLang === currentLang) return;

  if (!translations[newLang]) {
    showMessage(`Language "${newLang}" not supported.`, 'error');
    return;
  }

  currentLang = newLang;
  updateLanguage(newLang);
  updateActiveLanguageButton(e.target);
}

function updateLanguage(lang) {
  document.querySelectorAll('[data-i18n]').forEach(element => {
    const key = element.dataset.i18n;
    // Pour les éléments avec data-name-ar, utiliser la traduction arabe directement
    if (lang === 'ar' && element.dataset.nameAr) {
      element.textContent = element.dataset.nameAr;
    } else {
      element.textContent = translations[lang][key] || element.textContent;
    }
  });

  document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
    const key = element.dataset.i18nPlaceholder;
    element.placeholder = translations[lang][key] || element.placeholder;
  });

  document.querySelectorAll('[data-i18n-aria]').forEach(element => {
    const key = element.dataset.i18nAria;
    element.setAttribute('aria-label', translations[lang][key] || element.getAttribute('aria-label'));
  });

  // Update About section
  if (elements.aboutTitle) elements.aboutTitle.textContent = translations[lang]['about.title'] || elements.aboutTitle.textContent;
  if (elements.aboutDescription) elements.aboutDescription.textContent = translations[lang]['about.description'] || elements.aboutDescription.textContent;
  if (elements.aboutFeatures?.length >= 3) {
    elements.aboutFeatures[0].textContent = translations[lang]['about.feature1'] || elements.aboutFeatures[0].textContent;
    elements.aboutFeatures[1].textContent = translations[lang]['about.feature2'] || elements.aboutFeatures[1].textContent;
    elements.aboutFeatures[2].textContent = translations[lang]['about.feature3'] || elements.aboutFeatures[2].textContent;
  }
  if (elements.aboutFeatureDescriptions?.length >= 3) {
    elements.aboutFeatureDescriptions[0].textContent = translations[lang]['about.feature1_desc'] || elements.aboutFeatureDescriptions[0].textContent;
    elements.aboutFeatureDescriptions[1].textContent = translations[lang]['about.feature2_desc'] || elements.aboutFeatureDescriptions[1].textContent;
    elements.aboutFeatureDescriptions[2].textContent = translations[lang]['about.feature3_desc'] || elements.aboutFeatureDescriptions[2].textContent;
  }

  // Update document language and direction
  document.documentElement.lang = lang;
  document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';

  // Update body direction for proper layout
  document.body.style.direction = lang === 'ar' ? 'rtl' : 'ltr';
  document.body.style.textAlign = lang === 'ar' ? 'right' : 'left';
}

function updateActiveLanguageButton(activeBtn) {
  elements.langButtons.forEach(btn => {
    btn.classList.toggle('active', btn === activeBtn);
    btn.setAttribute('aria-pressed', btn === activeBtn ? 'true' : 'false');
  });
}

// Search functionality
function handleSearch() {
  const searchTerm = elements.searchBar.value.trim().toLowerCase();
  if (!searchTerm) {
    showMessage(translations[currentLang]['search.empty'], 'error');
    return;
  }

  const activeTab = document.querySelector('.category-tab.active');
  if (!activeTab) {
    showMessage('No category selected.', 'error');
    return;
  }

  const activeCategory = activeTab.dataset.category;
  const productCards = document.querySelectorAll(`#${activeCategory}-section .product-card`);
  let foundResults = false;

  productCards.forEach(card => {
    const productName = card.querySelector('.product-name').textContent.toLowerCase();
    card.style.display = productName.includes(searchTerm) ? '' : 'none';
    if (productName.includes(searchTerm)) foundResults = true;
  });

  if (!foundResults) {
    showMessage(translations[currentLang]['search.no_results'].replace('%s', searchTerm), 'info');
    setTimeout(() => productCards.forEach(card => card.style.display = ''), 3000);
  }
}

// Mobile menu functionality
function toggleMobileMenu() {
  const isOpen = elements.navMenu.classList.toggle('active');
  elements.body.classList.toggle('menu-open');
  elements.mobileMenuBtn.setAttribute('aria-expanded', isOpen);
  elements.mobileMenuBtn.textContent = isOpen ? '✕' : '☰';

  if (isOpen) {
    trapFocus(elements.navMenu);
  } else {
    removeFocusTrap(elements.navMenu);
  }
}

// Trap focus within an element
function trapFocus(element) {
  const focusableElements = element.querySelectorAll('a, button, input, [tabindex="0"]');
  if (!focusableElements.length) return;

  const firstFocusable = focusableElements[0];
  const lastFocusable = focusableElements[focusableElements.length - 1];

  const trap = e => {
    if (e.key === 'Tab') {
      if (e.shiftKey && document.activeElement === firstFocusable) {
        e.preventDefault();
        lastFocusable.focus();
      } else if (!e.shiftKey && document.activeElement === lastFocusable) {
        e.preventDefault();
        firstFocusable.focus();
      }
    }
  };

  element.addEventListener('keydown', trap);
  element.focusTrap = trap;
  firstFocusable.focus();
}

// Focus trap removal
function removeFocusTrap(element) {
  if (element.focusTrap) {
    element.removeEventListener('keydown', element.focusTrap);
    delete element.focusTrap;
  }
}

// Navigation click handler
function handleNavClick(e) {
  e.preventDefault();
  const targetId = e.target.getAttribute('href');
  if (targetId === '#') return;

  const targetSection = document.querySelector(targetId);
  if (targetSection) {
    targetSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    elements.navLinks.forEach(link => link.classList.remove('active'));
    e.target.classList.add('active');
    if (elements.navMenu.classList.contains('active')) toggleMobileMenu();
  }
}

// Category tab click handler
function handleCategoryTabClick() {
  const category = this.dataset.category;
  const targetSection = document.getElementById(`${category}-section`);
  if (!targetSection) return;

  document.querySelectorAll('.category-section').forEach(section => section.classList.add('hidden'));
  targetSection.classList.remove('hidden');

  elements.categoryTabs.forEach(tab => {
    tab.classList.toggle('active', tab === this);
    tab.setAttribute('aria-selected', tab === this ? 'true' : 'false');
  });

  document.getElementById('products').scrollIntoView({ behavior: 'smooth' });
}

// View details handler
function handleViewDetails(e) {
  const button = e.target;
  const productCard = button.closest('.product-card');
  if (!productCard) return;

  const productName = productCard.querySelector('.product-name').textContent;
  const productImageSrc = productCard.querySelector('.product-image img').src;
  const productImageAlt = productCard.querySelector('.product-image img').alt;
  const description = button.dataset[`description${currentLang === 'en' ? 'En' : 'Ar'}`] || 'Description not available.';

  if (!elements.modalTitle || !elements.modalImage || !elements.modalDescription) {
    console.error('Modal elements missing.');
    return;
  }

  elements.modalTitle.textContent = productName;
  elements.modalImage.src = productImageSrc;
  elements.modalImage.alt = productImageAlt;
  elements.modalDescription.textContent = description;

  elements.productModal.classList.add('active');
  elements.body.classList.add('modal-open');
  trapFocus(elements.productModal);
  elements.modalCloseButtons[0].focus();
}

// Close product modal
function closeProductModal() {
  if (!elements.productModal) return;

  elements.productModal.classList.remove('active');
  elements.body.classList.remove('modal-open');
  removeFocusTrap(elements.productModal);
}

// Modal focus trap
function handleModalFocusTrap(e) {
  const focusableElements = elements.productModal.querySelectorAll('button, [tabindex="0"]');
  if (!focusableElements.length) return;

  const firstFocusable = focusableElements[0];
  const lastFocusable = focusableElements[focusableElements.length - 1];

  if (e.key === 'Tab') {
    if (e.shiftKey && document.activeElement === firstFocusable) {
      e.preventDefault();
      lastFocusable.focus();
    } else if (!e.shiftKey && document.activeElement === lastFocusable) {
      e.preventDefault();
      firstFocusable.focus();
    }
  }
}

// Generic message display
function showMessage(message, type) {
  const messageEl = document.createElement('div');
  messageEl.textContent = message;
  messageEl.classList.add('notification', type);
  messageEl.style.cssText = `
    position: fixed;
    top: 16px;
    right: 16px;
    background: ${type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#3b82f6'};
    color: white;
    padding: 10px 16px;
    border-radius: 6px;
    z-index: 10000;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    font-size: 0.9rem;
    max-width: 90%;
  `;

  document.body.appendChild(messageEl);
  requestAnimationFrame(() => messageEl.style.transform = 'translateX(0)');

  setTimeout(() => {
    messageEl.style.transform = 'translateX(100%)';
    setTimeout(() => messageEl.remove(), 300);
  }, 3000);
}

// Intersection Observer for animations
function setupIntersectionObserver() {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('fade-in');
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.1 });

  document.querySelectorAll('.product-card, .feature, .category-section').forEach(el => observer.observe(el));
}

// Debounce utility
function debounce(fn, ms) {
  let timeout;
  return (...args) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => fn(...args), ms);
  };
}

// Handle window resize
function handleResize() {
  if (window.innerWidth >= 768 && elements.navMenu?.classList.contains('active')) {
    toggleMobileMenu();
  }
}

// Keyboard navigation
function handleKeyboardNavigation(e) {
  if (e.key === 'Escape' && elements.navMenu?.classList.contains('active')) {
    toggleMobileMenu();
  }
}

// Initialize when DOM is loaded
document.readyState === 'loading'
  ? document.addEventListener('DOMContentLoaded', init)
  : init();