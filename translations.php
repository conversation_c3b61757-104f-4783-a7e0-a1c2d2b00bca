<?php
// translations.php
require_once 'config.php';
require_once 'db_connect.php';

// Use configuration-based caching settings
$cache_file = TRANSLATION_CACHE_FILE;
$cache_ttl = TRANSLATION_CACHE_TTL;
$cache_enabled = TRANSLATION_CACHE_ENABLED;

$translations = [];

// Check cache only if caching is enabled and we're not in development mode
if ($cache_enabled && file_exists($cache_file) && (time() - filemtime($cache_file)) < $cache_ttl) {
    $translations = json_decode(file_get_contents($cache_file), true);

    // Log cache hit in development mode
    if (DEBUG_ENABLED) {
        file_put_contents('debug.log', "[" . date('Y-m-d H:i:s') . "] Translations loaded from cache\n", FILE_APPEND | LOCK_EX);
    }
} else {
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        $products = $conn->query("SELECT id, name, description_en, description_ar FROM products")->fetchAll();
        $categories = $conn->query("SELECT name_en, name_ar FROM categories")->fetchAll();

        $translations = [
    'en' => [
        'title' => 'Trusted Products',
        'home' => 'Home',
        'categories' => 'Categories',
        'products' => 'Products',
        'about' => 'About Us',
        'contact' => 'Contact',
        'admin' => 'Admin',
        'welcome' => 'Trusted Products for Everyday Life',
        'tagline' => 'Madjour Industries Quality You Can Count On',
        'view_details' => 'View Details',
        'category' => 'Category',
        'all_categories' => 'All Categories',
        'categories.lighters_&_gas' => 'Lighters & Gas',
        'categories.toothbrushes' => 'Toothbrushes',
        'categories.adhesive_glue' => 'Adhesive Glue',
        'categories.baby_products' => 'Baby Products',
        'categories.insecticides' => 'Insecticides'
    ],
    'ar' => [
        'title' => 'منتجات موثوقة',
        'home' => 'الرئيسية',
        'categories' => 'الفئات',
        'products' => 'منتجاتنا',
        'about' => 'من نحن',
        'contact' => 'اتصل بنا',
        'admin' => 'الإدارة',
        'welcome' => 'منتجات موثوقة للحياة اليومية',
        'tagline' => 'جودة صناعات مجور التي يمكنك الاعتماد عليها',
        'view_details' => 'عرض التفاصيل',
        'category' => 'الفئة',
        'all_categories' => 'جميع الفئات',
        'categories.lighters_&_gas' => 'الولاعات والغاز',
        'categories.toothbrushes' => 'فرش الأسنان',
        'categories.adhesive_glue' => 'الغراء اللاصق',
        'categories.baby_products' => 'منتجات الأطفال',
        'categories.insecticides' => 'المبيدات الحشرية'
    ]
];
        foreach ($products as $product) {
            $product_id = strtolower(str_replace(' ', '_', $product['name']));
            $translations['en']['products.' . $product_id] = $product['name'];
            $translations['ar']['products.' . $product_id] = $product['name'];
            $translations['en']['products.' . $product_id . '_desc'] = $product['description_en'];
            $translations['ar']['products.' . $product_id . '_desc'] = $product['description_ar'];
        }

        foreach ($categories as $category) {
            $category_slug = strtolower(str_replace(' ', '_', $category['name_en']));
            $translations['en']['categories.' . $category_slug] = $category['name_en'];
            $translations['ar']['categories.' . $category_slug] = $category['name_ar'];
        }

        // Save to cache only if caching is enabled
        if ($cache_enabled) {
            if (!is_dir(CACHE_DIR)) {
                mkdir(CACHE_DIR, 0755, true);
            }
            file_put_contents($cache_file, json_encode($translations));

            // Log cache write in development mode
            if (DEBUG_ENABLED) {
                file_put_contents('debug.log', "[" . date('Y-m-d H:i:s') . "] Translations saved to cache\n", FILE_APPEND | LOCK_EX);
            }
        } else {
            // Log cache bypass in development mode
            if (DEBUG_ENABLED) {
                file_put_contents('debug.log', "[" . date('Y-m-d H:i:s') . "] Translation caching disabled - loading fresh data\n", FILE_APPEND | LOCK_EX);
            }
        }
    } catch (Exception $e) {
        file_put_contents('debug.log', "translations.php: {$e->getMessage()}\n", FILE_APPEND);
        $translations = []; // Fallback to empty translations
    }
}

// Set appropriate cache headers based on development mode
if (isDevelopmentMode()) {
    // Development mode: prevent caching
    header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0');
    header('Pragma: no-cache');
    header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');
} else {
    // Production mode: allow caching
    header('Cache-Control: public, max-age=' . CACHE_TTL);
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + CACHE_TTL) . ' GMT');
}

// Set content type to JavaScript
header('Content-Type: application/javascript; charset=UTF-8');

// Output JavaScript
echo "const translations = " . json_encode($translations) . ";";
?>