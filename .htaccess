# Configuration UTF-8 pour les traductions
AddDefaultCharset UTF-8
AddCharset UTF-8 .php
AddCharset UTF-8 .html
AddCharset UTF-8 .css
AddCharset UTF-8 .js

# Headers pour le cache et UTF-8
<IfModule mod_headers.c>
    # Development mode: Force no-cache when NO_CACHE environment variable is set
    Header set Cache-Control "no-cache, no-store, must-revalidate, max-age=0" env=NO_CACHE
    Header set Pragma "no-cache" env=NO_CACHE
    Header set Expires "Thu, 01 Jan 1970 00:00:00 GMT" env=NO_CACHE

    # Production mode: Allow caching for static assets when NO_CACHE is not set
    Header set Cache-Control "public, max-age=3600" env=!NO_CACHE

    # Always set UTF-8 and security headers
    Header always set Content-Type "text/html; charset=UTF-8"
    Header set X-Content-Type-Options nosniff
    Header set X-Frame-Options SAMEORIGIN
    Header set X-XSS-Protection "1; mode=block"
</IfModule>

# Configuration MIME types
<IfModule mod_mime.c>
    AddType text/html .php
    AddType application/javascript .js
    AddType text/css .css
    AddType application/json .json
</IfModule>

# Configuration PHP pour UTF-8 (si supporté par l'hébergeur)
<IfModule mod_php.c>
    php_value default_charset "UTF-8"
    php_value mbstring.internal_encoding "UTF-8"
    php_value mbstring.http_output "UTF-8"
</IfModule>

# Cache control for static assets in production
<IfModule mod_expires.c>
    ExpiresActive On

    # CSS and JavaScript files - cache for 1 hour in production
    ExpiresByType text/css "access plus 1 hour"
    ExpiresByType application/javascript "access plus 1 hour"
    ExpiresByType text/javascript "access plus 1 hour"

    # Images - cache for 1 week
    ExpiresByType image/png "access plus 1 week"
    ExpiresByType image/jpg "access plus 1 week"
    ExpiresByType image/jpeg "access plus 1 week"
    ExpiresByType image/gif "access plus 1 week"
    ExpiresByType image/webp "access plus 1 week"
    ExpiresByType image/svg+xml "access plus 1 week"

    # Fonts - cache for 1 month
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
    ExpiresByType application/font-woff "access plus 1 month"
    ExpiresByType application/font-woff2 "access plus 1 month"
</IfModule>

# Disable caching for PHP files in development
<FilesMatch "\.(php)$">
    <IfModule mod_headers.c>
        Header set Cache-Control "no-cache, no-store, must-revalidate, max-age=0" env=NO_CACHE
        Header set Pragma "no-cache" env=NO_CACHE
        Header set Expires "Thu, 01 Jan 1970 00:00:00 GMT" env=NO_CACHE
    </IfModule>
</FilesMatch>

# Redirection d'erreur personnalisée
ErrorDocument 404 /index.php
ErrorDocument 500 /index.php
